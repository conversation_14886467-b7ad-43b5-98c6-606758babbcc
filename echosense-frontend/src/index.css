@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS Variables for Advanced Theming */
:root {
  --primary: #8A2BE2;
  --primary-dark: #6A1B9A;
  --primary-light: #BA68C8;
  --secondary: #4A90E2;
  --accent: #00D4AA;
  --dark: #0a0a0a;
  --dark-lighter: #1a1a1a;
  --glass-bg: rgba(10, 10, 10, 0.3);
  --glass-border: rgba(255, 255, 255, 0.1);
}

/* Advanced Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary), var(--secondary));
  border-radius: 4px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, var(--primary-light), var(--secondary));
  box-shadow: 0 0 10px rgba(138, 43, 226, 0.5);
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Advanced Glassmorphism Utilities */
.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: rgba(10, 10, 10, 0.6);
  backdrop-filter: blur(30px);
  -webkit-backdrop-filter: blur(30px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient Text Utilities */
.gradient-text {
  background: linear-gradient(45deg, var(--primary), var(--secondary), var(--accent));
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Advanced Glow Effects */
.glow-primary {
  box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-primary:hover {
  box-shadow: 0 0 30px rgba(138, 43, 226, 0.6);
}

.glow-secondary {
  box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
  transition: box-shadow 0.3s ease;
}

.glow-secondary:hover {
  box-shadow: 0 0 30px rgba(74, 144, 226, 0.6);
}

/* Pulse Animation */
.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 20px rgba(138, 43, 226, 0.3);
  }
  to {
    box-shadow: 0 0 40px rgba(138, 43, 226, 0.8);
  }
}

/* Floating Animation */
.float {
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* Advanced Hover Effects */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Responsive Typography */
.text-responsive-xl {
  font-size: clamp(1.5rem, 4vw, 3rem);
}

.text-responsive-lg {
  font-size: clamp(1.25rem, 3vw, 2rem);
}

.text-responsive-md {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
}

/* Loading Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0.1) 100%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Advanced Grid Layouts */
.grid-auto-fit {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.grid-auto-fill {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

/* Mobile-First Responsive Utilities */
@media (max-width: 640px) {
  .mobile-stack > * {
    width: 100% !important;
    margin-bottom: 1rem;
  }

  .mobile-hide {
    display: none !important;
  }

  .mobile-center {
    text-align: center !important;
  }
}

/* Tablet Responsive */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-2 {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

/* Desktop Enhancements */
@media (min-width: 1025px) {
  .desktop-grid-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }

  .desktop-grid-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }

  .print-break {
    page-break-after: always;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .glass {
    background: rgba(0, 0, 0, 0.9);
    border: 2px solid white;
  }
}

/* Focus Styles for Accessibility */
.focus-ring:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Custom Selection */
::selection {
  background: var(--primary);
  color: white;
}

::-moz-selection {
  background: var(--primary);
  color: white;
}

@layer base {
  body {
    @apply bg-dark text-gray-300 antialiased;
  }
  * {
    @apply scroll-smooth;
  }
}
