# FastAPI and server dependencies
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6
python-dotenv==1.0.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
asyncpg==0.29.0  # PostgreSQL async driver
aiosqlite==0.17.0  # SQLite async driver

# Data validation and serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP client for API calls
httpx==0.25.2
aiohttp==3.9.1

# Reddit API
praw==7.7.1
asyncpraw==7.7.1

# YouTube API
google-api-python-client==2.108.0
google-auth-httplib2==0.1.1
google-auth-oauthlib==1.1.0

# News API
newsapi-python==0.2.7

# Sentiment Analysis & NLP
transformers==4.35.2
torch==2.1.1
tokenizers==0.15.0
datasets==2.14.7
langdetect==1.0.9
googletrans==4.0.0rc1
bertopic==0.15.0
keybert==0.8.3

# Forecasting
prophet==1.1.5
pandas==2.1.3
numpy==1.25.2
scikit-learn==1.3.2

# AI/LLM Integration
langchain==0.0.340
openai==1.3.7

# Background tasks and scheduling
celery==5.3.4
redis==5.0.1
apscheduler==3.10.4

# Utilities
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-dateutil==2.8.2
pytz==2023.3

# Logging and monitoring
structlog==23.2.0
rich==13.7.0

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
isort==5.12.0
flake8==6.1.0

# CORS and middleware
# CORS is handled by FastAPI middleware
