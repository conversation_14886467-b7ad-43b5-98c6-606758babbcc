2025-08-13 15:52:40,949 - __main__ - INFO - Starting EchoSense Backend Server...
2025-08-13 15:52:40,949 - __main__ - INFO - Environment: development
2025-08-13 15:52:40,949 - __main__ - INFO - Backend Port: 8000
2025-08-13 15:52:40,949 - __main__ - INFO - Demo Mode: True
2025-08-13 15:52:42,180 - backend.main - INFO - Starting EchoSense Backend Services...
2025-08-13 15:52:42,180 - backend.data_ingestion - INFO - Initializing Data Ingestion Service...
2025-08-13 15:52:43,383 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-13 15:52:43,398 - backend.data_ingestion - INFO - Data Ingestion Service initialized successfully
2025-08-13 15:52:43,399 - backend.sentiment_analysis - INFO - Initializing Sentiment Analysis Service...
2025-08-13 15:52:43,399 - backend.sentiment_analysis - INFO - Using CPU for sentiment analysis
2025-08-13 15:52:43,399 - backend.sentiment_analysis - INFO - Loading model: distilbert-base-uncased-finetuned-sst-2-english
2025-08-13 15:53:23,269 - backend.sentiment_analysis - INFO - Sentiment Analysis Service initialized successfully
2025-08-13 15:53:23,269 - backend.forecasting - INFO - Initializing Forecasting Service...
2025-08-13 15:53:23,269 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:53:23,272 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:53:23,272 - backend.forecasting - INFO - Forecasting Service initialized successfully
2025-08-13 15:53:23,272 - backend.ai_agent - INFO - Initializing AI Agent Service...
2025-08-13 15:53:23,318 - backend.ai_agent - INFO - AI Agent Service initialized successfully
2025-08-13 15:53:23,318 - backend.main - INFO - All services initialized successfully
2025-08-13 15:53:23,318 - backend.main - INFO - Starting continuous data collection...
2025-08-13 15:53:23,318 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 15:53:24,761 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 15:53:25,404 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 15:53:25,404 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 15:53:25,405 - backend.data_ingestion - INFO - Collected 30 items from YouTube
2025-08-13 15:53:25,405 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 15:53:25,405 - backend.data_ingestion - INFO - Total unique items collected: 30
2025-08-13 15:53:25,405 - backend.sentiment_analysis - INFO - Analyzing sentiment for 30 items...
2025-08-13 15:53:27,272 - backend.sentiment_analysis - INFO - Completed sentiment analysis for 30 items
2025-08-13 15:53:27,281 - backend.main - INFO - Processed 30 new items
2025-08-13 15:55:14,876 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:14,880 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:14,880 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:16,014 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:16,016 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:16,016 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:17,059 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:17,061 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:17,061 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:17,955 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:17,956 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:17,956 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:17,963 - backend.ai_agent - INFO - Generating AI responses for 5 posts...
2025-08-13 15:55:19,646 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:19,648 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:19,648 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:19,737 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:19,738 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:19,739 - openai._base_client - INFO - Retrying request to /completions in 0.957962 seconds
2025-08-13 15:55:19,739 - openai._base_client - INFO - Retrying request to /completions in 0.808646 seconds
2025-08-13 15:55:19,782 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:19,783 - openai._base_client - INFO - Retrying request to /completions in 0.821071 seconds
2025-08-13 15:55:19,790 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:19,791 - openai._base_client - INFO - Retrying request to /completions in 0.847599 seconds
2025-08-13 15:55:20,156 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:20,157 - openai._base_client - INFO - Retrying request to /completions in 0.798555 seconds
2025-08-13 15:55:20,456 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:20,458 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:20,458 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:21,159 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:21,161 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:21,161 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:21,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:21,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:21,258 - openai._base_client - INFO - Retrying request to /completions in 1.705139 seconds
2025-08-13 15:55:21,258 - openai._base_client - INFO - Retrying request to /completions in 1.846058 seconds
2025-08-13 15:55:21,452 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:21,453 - openai._base_client - INFO - Retrying request to /completions in 1.664339 seconds
2025-08-13 15:55:21,605 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:21,605 - openai._base_client - INFO - Retrying request to /completions in 1.731884 seconds
2025-08-13 15:55:21,615 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:21,617 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:21,617 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:21,620 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:21,621 - openai._base_client - INFO - Retrying request to /completions in 1.844184 seconds
2025-08-13 15:55:21,965 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:21,967 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:21,967 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:22,147 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:22,149 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:22,149 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:22,603 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:22,605 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:22,605 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:22,735 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:22,737 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:22,737 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:22,851 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:22,853 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:22,853 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,001 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:23,020 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:23,020 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,154 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:23,156 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:23,156 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,285 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:23,286 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:23,287 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,417 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:23,419 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:23,419 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,494 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:23,495 - backend.ai_agent - ERROR - Error generating AI response: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-13 15:55:23,582 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 15:55:23,584 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 15:55:23,584 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 15:55:23,685 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:23,686 - backend.ai_agent - ERROR - Error generating AI response: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-13 15:55:23,856 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:23,857 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:23,858 - backend.ai_agent - ERROR - Error generating AI response: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-13 15:55:23,859 - backend.ai_agent - ERROR - Error generating AI response: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-13 15:55:24,051 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/completions "HTTP/1.1 429 Too Many Requests"
2025-08-13 15:55:24,053 - backend.ai_agent - ERROR - Error generating AI response: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-08-13 15:55:24,056 - backend.ai_agent - INFO - Successfully generated 5 AI responses
2025-08-13 15:55:27,284 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 15:55:28,532 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 15:55:29,824 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 15:55:29,825 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 15:55:29,826 - backend.data_ingestion - INFO - Collected 30 items from YouTube
2025-08-13 15:55:29,826 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 15:55:29,826 - backend.data_ingestion - INFO - Total unique items collected: 30
2025-08-13 15:55:29,826 - backend.sentiment_analysis - INFO - Analyzing sentiment for 30 items...
2025-08-13 15:55:30,293 - backend.sentiment_analysis - INFO - Completed sentiment analysis for 30 items
2025-08-13 15:55:30,301 - backend.main - INFO - Processed 30 new items
2025-08-13 15:57:30,285 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 15:57:30,623 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 15:57:30,623 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A27%3A30.287824Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 15:57:30,623 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 15:57:31,552 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 15:57:31,552 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 15:57:31,553 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 15:57:31,553 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 15:57:31,553 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 15:59:31,555 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 15:59:31,864 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 15:59:31,864 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A29%3A31.558584Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 15:59:31,864 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 15:59:32,505 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 15:59:32,505 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 15:59:32,505 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 15:59:32,505 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 15:59:32,505 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:01:32,508 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:01:32,927 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:01:32,928 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A31%3A32.513160Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:01:32,928 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:01:33,919 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:01:33,919 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:01:33,919 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:01:33,920 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:01:33,920 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:03:33,921 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:03:34,283 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:03:34,283 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A33%3A33.924794Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:03:34,283 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:03:35,162 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:03:35,163 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:03:35,163 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:03:35,163 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:03:35,163 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:05:35,165 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:05:35,369 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:05:35,369 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A35%3A35.171103Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:05:35,369 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:05:36,374 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:05:36,376 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:05:36,377 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:05:36,377 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:05:36,377 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:06:17,867 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:06:17,896 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:06:17,897 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:06:17,925 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:06:17,930 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:06:17,930 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:06:17,937 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:06:17,939 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:06:17,939 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:07:35,126 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:07:35,146 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:07:35,147 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:07:35,167 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:07:35,177 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:07:35,177 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:07:35,203 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:07:35,206 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:07:35,206 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:07:36,382 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:07:37,332 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:07:37,333 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A37%3A36.395313Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:07:37,333 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:07:38,126 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:07:38,130 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:07:38,130 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:07:38,130 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:07:38,130 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:08:22,037 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:08:22,044 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:08:22,045 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:08:22,053 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:08:22,056 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:08:22,056 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:09:38,133 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:09:38,771 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:09:38,772 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A39%3A38.140175Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:09:38,772 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:09:39,528 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:09:39,529 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:09:39,529 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:09:39,529 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:09:39,529 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:09:49,042 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:09:49,051 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:09:49,051 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:09:49,066 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:09:49,070 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:09:49,070 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:10:53,426 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:10:53,431 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:10:53,431 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:10:53,440 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:10:53,443 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:10:53,443 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:10:54,877 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:10:54,880 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:10:54,880 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:10:54,889 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:10:54,891 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:10:54,892 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:11:39,532 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:11:39,711 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:11:39,712 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A41%3A39.548504Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:11:39,712 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:11:40,317 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:11:40,318 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:11:40,318 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:11:40,319 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:11:40,319 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:13:40,294 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:13:41,821 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:13:41,821 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A43%3A40.298129Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:13:41,822 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:13:44,295 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:13:44,295 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:13:44,296 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:13:44,296 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:13:44,296 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:15:44,297 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:15:44,656 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:15:44,657 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A45%3A44.301468Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:15:44,657 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:15:45,724 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:15:45,724 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:15:45,724 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:15:45,724 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:15:45,724 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:17:45,726 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:17:46,116 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:17:46,117 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A47%3A45.730740Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:17:46,117 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:17:47,127 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:17:47,128 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:17:47,128 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:17:47,128 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:17:47,128 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:19:47,131 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:19:47,450 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:19:47,450 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A49%3A47.134601Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:19:47,450 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:19:48,259 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:19:48,259 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:19:48,259 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:19:48,260 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:19:48,260 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:21:48,262 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:21:48,629 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:21:48,629 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A51%3A48.268515Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:21:48,629 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:21:49,843 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:21:49,844 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:21:49,844 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:21:49,844 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:21:49,844 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:23:49,846 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:23:50,213 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:23:50,213 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A53%3A49.850440Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:23:50,214 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:23:51,013 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:23:51,014 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:23:51,014 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:23:51,014 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:23:51,014 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:25:51,016 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:25:51,358 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:25:51,358 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T10%3A55%3A51.019256Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:25:51,358 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:25:52,947 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:25:52,948 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:25:52,948 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:25:52,948 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:25:52,948 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:25:54,882 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:25:54,886 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:25:54,886 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:25:54,892 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:25:54,895 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:25:54,895 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,820 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,827 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,828 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,839 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,845 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,845 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,858 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,862 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,863 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,872 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,876 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,876 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,908 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,914 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,915 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,926 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,930 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,931 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,940 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,944 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,944 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:43,952 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:43,954 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:43,954 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:44,124 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:44,128 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:44,128 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:26:44,138 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:26:44,141 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:26:44,141 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:29:40,837 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:30:40,871 - backend.data_ingestion - ERROR - Error collecting YouTube data: The read operation timed out
2025-08-13 16:30:40,872 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:30:40,883 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request 
2025-08-13 16:30:40,884 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:30:40,884 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:30:40,885 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:30:40,885 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:32:40,872 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:32:41,610 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:32:41,611 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A02%3A40.876150Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:32:41,611 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:32:42,740 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:32:42,740 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:32:42,741 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:32:42,741 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:32:42,741 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:34:42,743 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:34:43,174 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:34:43,175 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A04%3A42.750371Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:34:43,175 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:34:44,018 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:34:44,018 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:34:44,020 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:34:44,020 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:34:44,020 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:36:44,022 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:36:44,536 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:36:44,536 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A06%3A44.032223Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:36:44,537 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:36:45,124 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:36:45,125 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:36:45,125 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:36:45,125 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:36:45,125 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:38:45,128 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:38:45,291 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:38:45,291 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A08%3A45.135404Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:38:45,291 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:38:45,917 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:38:45,917 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:38:45,917 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:38:45,918 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:38:45,918 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:39:18,804 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:39:18,808 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:39:18,810 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:39:18,820 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:39:18,824 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:39:18,824 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 16:40:45,920 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:40:46,053 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:40:46,053 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A10%3A45.931923Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:40:46,054 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:40:46,793 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:40:46,794 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:40:46,794 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:40:46,795 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:40:46,795 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:46:57,464 - __main__ - INFO - Starting EchoSense Backend Server...
2025-08-13 16:46:57,465 - __main__ - INFO - Environment: development
2025-08-13 16:46:57,465 - __main__ - INFO - Backend Port: 8000
2025-08-13 16:46:57,465 - __main__ - INFO - Demo Mode: True
2025-08-13 16:46:57,557 - backend.main - INFO - Starting EchoSense Backend Services...
2025-08-13 16:46:57,557 - backend.data_ingestion - INFO - Initializing Data Ingestion Service...
2025-08-13 16:46:57,563 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-13 16:46:57,573 - backend.data_ingestion - INFO - Data Ingestion Service initialized successfully
2025-08-13 16:46:57,573 - backend.sentiment_analysis - INFO - Initializing Sentiment Analysis Service...
2025-08-13 16:46:57,573 - backend.sentiment_analysis - INFO - Using CPU for sentiment analysis
2025-08-13 16:46:57,573 - backend.sentiment_analysis - INFO - Loading model: distilbert-base-uncased-finetuned-sst-2-english
2025-08-13 16:46:59,952 - backend.sentiment_analysis - INFO - Sentiment Analysis Service initialized successfully
2025-08-13 16:46:59,952 - backend.advanced_sentiment - INFO - Initializing Advanced Sentiment Analysis Service...
2025-08-13 16:46:59,953 - backend.advanced_sentiment - WARNING - Translation service not available
2025-08-13 16:46:59,953 - backend.advanced_sentiment - INFO - Using CPU for advanced sentiment analysis
2025-08-13 16:47:36,929 - backend.advanced_sentiment - INFO - Transformers models initialized
2025-08-13 16:47:36,930 - backend.advanced_sentiment - INFO - Advanced Sentiment Analysis Service initialized successfully
2025-08-13 16:47:36,930 - backend.crisis_detection - INFO - Initializing Crisis Detection Service...
2025-08-13 16:47:36,946 - backend.crisis_detection - INFO - Crisis Detection Service initialized successfully
2025-08-13 16:47:36,947 - backend.topic_modeling - INFO - Initializing Topic Modeling Service...
2025-08-13 16:47:36,965 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-13 16:48:58,147 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-13 16:48:59,233 - backend.topic_modeling - INFO - Topic modeling models initialized
2025-08-13 16:48:59,260 - backend.topic_modeling - INFO - Topic Modeling Service initialized successfully
2025-08-13 16:48:59,278 - backend.forecasting - INFO - Initializing Forecasting Service...
2025-08-13 16:48:59,279 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:48:59,282 - backend.forecasting - ERROR - Error training model: no such table: posts
2025-08-13 16:48:59,283 - backend.forecasting - INFO - Forecasting Service initialized successfully
2025-08-13 16:48:59,283 - backend.ai_agent - INFO - Initializing AI Agent Service...
2025-08-13 16:48:59,288 - backend.ai_agent - ERROR - Failed to initialize AI Agent Service: 1 validation error for OpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-08-13 16:48:59,289 - backend.ai_agent - WARNING - AI Agent will use fallback responses
2025-08-13 16:48:59,289 - backend.main - INFO - All services initialized successfully
2025-08-13 16:48:59,289 - backend.main - INFO - Starting continuous monitoring...
2025-08-13 16:48:59,289 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:48:59,661 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:48:59,661 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A18%3A59.339325Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:48:59,661 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:49:00,168 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:49:00,169 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:49:00,169 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:49:00,170 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:49:00,170 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:49:00,172 - backend.crisis_detection - ERROR - Error detecting sentiment anomalies: no such table: posts
2025-08-13 16:49:00,174 - backend.crisis_detection - ERROR - Error detecting volume anomalies: no such table: posts
2025-08-13 16:49:00,174 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 16:50:41,312 - backend.main - ERROR - Error getting brand health: no such table: posts
2025-08-13 16:51:00,177 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:51:00,348 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:51:00,349 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A21%3A00.195838Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:51:00,350 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:51:00,959 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:51:00,960 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:51:00,960 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:51:00,960 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:51:00,960 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:51:00,963 - backend.crisis_detection - ERROR - Error detecting sentiment anomalies: no such table: posts
2025-08-13 16:51:00,964 - backend.crisis_detection - ERROR - Error detecting volume anomalies: no such table: posts
2025-08-13 16:51:00,965 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 16:53:00,966 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:53:01,108 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:53:01,109 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A23%3A00.969630Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:53:01,109 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:53:01,686 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:53:01,686 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:53:01,686 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:53:01,686 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:53:01,686 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:53:01,689 - backend.crisis_detection - ERROR - Error detecting sentiment anomalies: no such table: posts
2025-08-13 16:53:01,691 - backend.crisis_detection - ERROR - Error detecting volume anomalies: no such table: posts
2025-08-13 16:53:01,691 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 16:54:24,636 - backend.main - ERROR - Error getting feed: no such table: posts
2025-08-13 16:54:31,148 - backend.main - ERROR - Error getting stats: no such table: posts
2025-08-13 16:55:01,693 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:55:01,834 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:55:01,834 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A25%3A01.696702Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:55:01,834 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:55:02,499 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:55:02,500 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:55:02,500 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:55:02,500 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:55:02,500 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:55:02,502 - backend.crisis_detection - ERROR - Error detecting sentiment anomalies: no such table: posts
2025-08-13 16:55:02,503 - backend.crisis_detection - ERROR - Error detecting volume anomalies: no such table: posts
2025-08-13 16:55:02,503 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 16:56:29,585 - __main__ - INFO - Starting EchoSense Backend Server...
2025-08-13 16:56:29,585 - __main__ - INFO - Environment: development
2025-08-13 16:56:29,586 - __main__ - INFO - Backend Port: 8000
2025-08-13 16:56:29,586 - __main__ - INFO - Demo Mode: True
2025-08-13 16:56:29,679 - backend.main - INFO - Starting EchoSense Backend Services...
2025-08-13 16:56:29,679 - backend.data_ingestion - INFO - Initializing Data Ingestion Service...
2025-08-13 16:56:30,309 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-13 16:56:30,328 - backend.data_ingestion - INFO - Inserted 6 demo posts
2025-08-13 16:56:30,329 - backend.data_ingestion - INFO - Data Ingestion Service initialized successfully
2025-08-13 16:56:30,329 - backend.sentiment_analysis - INFO - Initializing Sentiment Analysis Service...
2025-08-13 16:56:30,329 - backend.sentiment_analysis - INFO - Using CPU for sentiment analysis
2025-08-13 16:56:30,329 - backend.sentiment_analysis - INFO - Loading model: distilbert-base-uncased-finetuned-sst-2-english
2025-08-13 16:56:32,417 - backend.sentiment_analysis - INFO - Sentiment Analysis Service initialized successfully
2025-08-13 16:56:32,417 - backend.advanced_sentiment - INFO - Initializing Advanced Sentiment Analysis Service...
2025-08-13 16:56:32,417 - backend.advanced_sentiment - WARNING - Translation service not available
2025-08-13 16:56:32,418 - backend.advanced_sentiment - INFO - Using CPU for advanced sentiment analysis
2025-08-13 16:56:35,694 - backend.advanced_sentiment - INFO - Transformers models initialized
2025-08-13 16:56:35,694 - backend.advanced_sentiment - INFO - Advanced Sentiment Analysis Service initialized successfully
2025-08-13 16:56:35,694 - backend.crisis_detection - INFO - Initializing Crisis Detection Service...
2025-08-13 16:56:35,699 - backend.crisis_detection - INFO - Crisis Detection Service initialized successfully
2025-08-13 16:56:35,699 - backend.topic_modeling - INFO - Initializing Topic Modeling Service...
2025-08-13 16:56:35,706 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-13 16:56:40,682 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-13 16:56:41,718 - backend.topic_modeling - INFO - Topic modeling models initialized
2025-08-13 16:56:41,718 - backend.topic_modeling - INFO - Topic Modeling Service initialized successfully
2025-08-13 16:56:41,718 - backend.forecasting - INFO - Initializing Forecasting Service...
2025-08-13 16:56:41,718 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 16:56:41,721 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 16:56:41,721 - backend.forecasting - INFO - Forecasting Service initialized successfully
2025-08-13 16:56:41,721 - backend.ai_agent - INFO - Initializing AI Agent Service...
2025-08-13 16:56:41,726 - backend.ai_agent - ERROR - Failed to initialize AI Agent Service: 1 validation error for OpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-08-13 16:56:41,726 - backend.ai_agent - WARNING - AI Agent will use fallback responses
2025-08-13 16:56:41,727 - backend.main - INFO - All services initialized successfully
2025-08-13 16:56:41,727 - backend.main - INFO - Starting continuous monitoring...
2025-08-13 16:56:41,727 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:56:42,400 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:56:42,401 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A26%3A41.758998Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:56:42,401 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:56:43,201 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:56:43,202 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:56:43,202 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:56:43,202 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:56:43,202 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:56:43,208 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 16:58:43,210 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 16:58:44,674 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 16:58:44,674 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A28%3A43.217321Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 16:58:44,674 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 16:58:52,517 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 16:58:52,519 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 16:58:52,519 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 16:58:52,519 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 16:58:52,519 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 16:58:52,526 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:00:52,527 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:04:26,162 - __main__ - INFO - Starting EchoSense Backend Server...
2025-08-13 17:04:26,163 - __main__ - INFO - Environment: development
2025-08-13 17:04:26,163 - __main__ - INFO - Backend Port: 8000
2025-08-13 17:04:26,163 - __main__ - INFO - Demo Mode: True
2025-08-13 17:04:26,256 - backend.main - INFO - Starting EchoSense Backend Services...
2025-08-13 17:04:26,256 - backend.data_ingestion - INFO - Initializing Data Ingestion Service...
2025-08-13 17:04:26,261 - googleapiclient.discovery_cache - INFO - file_cache is only supported with oauth2client<4.0.0
2025-08-13 17:04:26,272 - backend.data_ingestion - INFO - Data Ingestion Service initialized successfully
2025-08-13 17:04:26,272 - backend.sentiment_analysis - INFO - Initializing Sentiment Analysis Service...
2025-08-13 17:04:26,273 - backend.sentiment_analysis - INFO - Using CPU for sentiment analysis
2025-08-13 17:04:26,273 - backend.sentiment_analysis - INFO - Loading model: distilbert-base-uncased-finetuned-sst-2-english
2025-08-13 17:04:28,572 - backend.sentiment_analysis - INFO - Sentiment Analysis Service initialized successfully
2025-08-13 17:04:28,573 - backend.advanced_sentiment - INFO - Initializing Advanced Sentiment Analysis Service...
2025-08-13 17:04:28,573 - backend.advanced_sentiment - WARNING - Translation service not available
2025-08-13 17:04:28,573 - backend.advanced_sentiment - INFO - Using CPU for advanced sentiment analysis
2025-08-13 17:04:31,971 - backend.advanced_sentiment - INFO - Transformers models initialized
2025-08-13 17:04:31,972 - backend.advanced_sentiment - INFO - Advanced Sentiment Analysis Service initialized successfully
2025-08-13 17:04:31,972 - backend.crisis_detection - INFO - Initializing Crisis Detection Service...
2025-08-13 17:04:31,974 - backend.crisis_detection - INFO - Crisis Detection Service initialized successfully
2025-08-13 17:04:31,974 - backend.topic_modeling - INFO - Initializing Topic Modeling Service...
2025-08-13 17:04:31,978 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-08-13 17:04:36,229 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: mps
2025-08-13 17:04:37,283 - backend.topic_modeling - INFO - Topic modeling models initialized
2025-08-13 17:04:37,283 - backend.topic_modeling - INFO - Topic Modeling Service initialized successfully
2025-08-13 17:04:37,283 - backend.forecasting - INFO - Initializing Forecasting Service...
2025-08-13 17:04:37,283 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 17:04:37,287 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 17:04:37,287 - backend.forecasting - INFO - Forecasting Service initialized successfully
2025-08-13 17:04:37,287 - backend.ai_agent - INFO - Initializing AI Agent Service...
2025-08-13 17:04:37,292 - backend.ai_agent - ERROR - Failed to initialize AI Agent Service: 1 validation error for OpenAI
__root__
  Client.__init__() got an unexpected keyword argument 'proxies' (type=type_error)
2025-08-13 17:04:37,293 - backend.ai_agent - WARNING - AI Agent will use fallback responses
2025-08-13 17:04:37,293 - backend.main - INFO - All services initialized successfully
2025-08-13 17:04:37,293 - backend.main - INFO - Starting continuous monitoring...
2025-08-13 17:04:37,293 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:04:38,310 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:04:38,311 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A34%3A37.321502Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:04:38,311 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:04:39,256 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:04:39,256 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:04:39,256 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:04:39,256 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:04:39,257 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:04:39,263 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:06:39,264 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:06:39,447 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:06:39,447 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A36%3A39.267277Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:06:39,447 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:06:40,180 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:06:40,180 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:06:40,181 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:06:40,181 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:06:40,181 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:06:40,186 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:06:47,980 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 17:06:47,987 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 17:06:47,987 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 17:06:48,008 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 17:06:48,012 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 17:06:48,012 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 17:06:51,808 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 17:06:51,819 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 17:06:51,819 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 17:06:51,839 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 17:06:51,842 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 17:06:51,843 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 17:08:40,193 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:08:40,663 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:08:40,666 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A38%3A40.306328Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:08:40,667 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:08:41,584 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:08:41,585 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:08:41,586 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:08:41,586 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:08:41,586 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:08:41,602 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:10:41,605 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:10:41,793 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:10:41,794 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A40%3A41.612987Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:10:41,794 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:10:42,611 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:10:42,612 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:10:42,612 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:10:42,613 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:10:42,613 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:10:42,623 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:12:42,623 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:12:42,835 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:12:42,836 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A42%3A42.644674Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:12:42,836 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:12:43,481 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:12:43,482 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:12:43,482 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:12:43,483 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:12:43,483 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:12:43,491 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:14:43,492 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:14:44,955 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:14:44,955 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A44%3A43.496988Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:14:44,956 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:14:48,980 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:14:48,981 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:14:48,981 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:14:48,981 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:14:48,981 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:14:48,989 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 17:16:48,990 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 17:16:49,296 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 17:16:49,297 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T11%3A46%3A48.996644Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 17:16:49,297 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 17:16:50,496 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 17:16:50,497 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 17:16:50,497 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 17:16:50,497 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 17:16:50,497 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 17:16:50,503 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 19:18:28,522 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 19:18:28,533 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 19:18:28,535 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 19:18:28,561 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 19:18:28,567 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 19:18:28,568 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 20:20:16,840 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:21:20,097 - backend.data_ingestion - ERROR - Error collecting YouTube data: The read operation timed out
2025-08-13 20:21:20,098 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:21:20,111 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request 
2025-08-13 20:21:20,114 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:21:20,115 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:21:20,115 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:21:20,115 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:21:20,129 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:23:20,279 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:23:21,012 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 20:23:21,012 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T14%3A53%3A20.287183Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 20:23:21,012 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:23:21,622 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 20:23:21,624 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:23:21,624 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:23:21,624 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:23:21,624 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:23:21,633 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:25:21,635 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:25:22,013 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 20:25:22,014 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T14%3A55%3A21.641825Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 20:25:22,014 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:25:22,553 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 20:25:22,553 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:25:22,553 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:25:22,553 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:25:22,554 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:25:22,562 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:27:22,128 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 20:27:22,278 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 20:27:22,278 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 20:27:22,291 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 20:27:22,294 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 20:27:22,294 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 20:27:22,564 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:27:22,900 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 20:27:22,900 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T14%3A57%3A22.566837Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 20:27:22,900 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:27:25,723 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 20:27:25,724 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:27:25,724 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:27:25,724 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:27:25,724 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:27:25,730 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:32:50,664 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:33:23,051 - backend.data_ingestion - ERROR - Error collecting YouTube data: [Errno 8] nodename nor servname provided, or not known
2025-08-13 20:33:23,052 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:33:23,052 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request 
2025-08-13 20:33:23,053 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:33:23,053 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:33:23,053 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:33:23,053 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:33:23,062 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:34:54,949 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 20:34:54,955 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 20:34:54,955 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 20:34:54,963 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 20:34:54,966 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 20:34:54,966 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 20:36:08,897 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:36:08,905 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 20:36:08,905 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:36:08,910 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 20:36:08,910 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:36:08,910 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:36:08,910 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:36:08,910 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:36:08,915 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:39:40,518 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:39:40,531 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 20:39:40,531 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:39:40,535 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 20:39:40,537 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:39:40,537 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:39:40,538 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:39:40,538 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:39:40,557 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 20:43:12,159 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 20:43:12,170 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 20:43:12,171 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 20:43:12,176 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 20:43:12,177 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 20:43:12,177 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 20:43:12,178 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 20:43:12,179 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 20:43:12,196 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:00:08,011 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:00:08,021 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:00:08,022 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:00:08,026 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:00:08,028 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:00:08,028 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:00:08,028 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:00:08,029 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:00:08,048 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:00:08,069 - backend.topic_modeling - WARNING - Not enough posts for topic modeling
2025-08-13 21:00:08,077 - backend.main - INFO - Topic extraction completed
2025-08-13 21:17:02,947 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:17:02,956 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:17:02,956 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:17:02,960 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:17:02,960 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:17:02,961 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:17:02,961 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:17:02,961 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:17:02,980 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:19:02,986 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:19:02,993 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:19:02,993 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:19:02,995 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:19:02,995 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:19:02,995 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:19:02,996 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:19:02,996 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:19:03,003 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:21:03,006 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:21:03,012 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:21:03,013 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:21:03,015 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:21:03,015 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:21:03,015 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:21:03,015 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:21:03,015 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:21:03,021 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:23:03,023 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:23:03,028 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:23:03,028 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:23:03,030 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:23:03,030 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:23:03,030 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:23:03,030 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:23:03,030 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:23:03,037 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:25:03,039 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:25:03,045 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:25:03,045 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:25:03,047 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:25:03,047 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:25:03,047 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:25:03,047 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:25:03,047 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:25:03,053 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:27:03,057 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:27:03,062 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:27:03,062 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:27:03,064 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:27:03,064 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:27:03,064 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:27:03,064 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:27:03,064 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:27:03,070 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:29:03,074 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:29:03,080 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:29:03,080 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:29:03,082 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:29:03,082 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:29:03,082 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:29:03,082 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:29:03,082 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:29:03,087 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 21:31:03,089 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 21:31:03,094 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 21:31:03,094 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 21:31:03,097 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 21:31:03,097 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 21:31:03,097 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 21:31:03,097 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 21:31:03,097 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 21:31:03,103 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:25:11,729 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:25:11,749 - backend.data_ingestion - ERROR - Error collecting YouTube data: Unable to find the server at youtube.googleapis.com
2025-08-13 22:25:11,749 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:25:11,751 - backend.data_ingestion - ERROR - Error collecting Reddit data: error with request Cannot connect to host www.reddit.com:443 ssl:default [nodename nor servname provided, or not known]
2025-08-13 22:25:11,752 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:25:11,752 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:25:11,752 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:25:11,752 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:25:11,759 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:27:11,754 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:27:12,739 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:27:12,740 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T16%3A57%3A11.757668Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:27:12,741 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:27:15,735 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:27:15,736 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:27:15,736 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:27:15,736 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:27:15,736 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:27:15,741 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:29:15,743 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:29:16,136 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:29:16,136 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T16%3A59%3A15.745938Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:29:16,136 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:29:16,811 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:29:16,812 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:29:16,812 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:29:16,812 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:29:16,812 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:29:16,817 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:31:16,819 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:31:17,198 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:31:17,198 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A01%3A16.822686Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:31:17,198 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:31:18,088 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:31:18,089 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:31:18,089 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:31:18,089 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:31:18,089 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:31:18,097 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:36:57,834 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:37:00,156 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:37:00,157 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A06%3A58.242624Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:37:00,157 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:37:01,129 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:37:01,135 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:37:01,136 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:37:01,136 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:37:01,136 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:37:01,150 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:39:01,152 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:39:01,777 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:39:01,777 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A09%3A01.156191Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:39:01,777 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:39:02,486 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:39:02,487 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:39:02,487 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:39:02,487 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:39:02,488 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:39:02,495 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:40:09,398 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:40:09,420 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:40:09,420 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:40:09,438 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:40:09,445 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:40:09,445 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:41:02,496 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:41:03,038 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:41:03,040 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A11%3A02.501438Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:41:03,040 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:41:04,081 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:41:04,081 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:41:04,082 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:41:04,082 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:41:04,082 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:41:04,088 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:41:21,914 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:41:21,920 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:41:21,921 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:41:51,137 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:41:51,142 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:41:51,142 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:41:51,154 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:41:51,219 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:41:51,219 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:43:04,090 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:43:04,297 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:43:04,297 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A13%3A04.093624Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:43:04,297 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:43:04,937 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:43:04,938 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:43:04,938 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:43:04,938 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:43:04,938 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:43:04,945 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:43:13,913 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:43:13,919 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:43:13,920 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:45:04,776 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:45:05,109 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:45:05,110 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A15%3A04.782595Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:45:05,110 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:45:05,617 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:45:05,619 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:45:05,619 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:45:05,619 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:45:05,619 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:45:05,626 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:45:32,763 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:45:32,770 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:45:32,770 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:45:32,780 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:45:32,783 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:45:32,783 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:46:06,199 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:46:06,203 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:46:06,204 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:46:06,216 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:46:06,220 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:46:06,220 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:46:31,972 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:46:31,993 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:46:31,993 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:46:32,007 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:46:32,013 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:46:32,015 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:47:05,626 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:47:06,093 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:47:06,093 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A17%3A05.629474Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:47:06,093 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:47:07,332 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:47:07,332 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:47:07,333 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:47:07,333 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:47:07,333 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:47:07,338 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:49:07,339 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:49:07,931 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:49:07,931 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A19%3A07.343853Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:49:07,932 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:49:12,449 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:49:12,450 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:49:12,450 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:49:12,450 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:49:12,450 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:49:12,458 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:51:12,458 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:51:12,864 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:51:12,865 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A21%3A12.464998Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:51:12,865 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:51:13,478 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:51:13,478 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:51:13,478 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:51:13,478 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:51:13,478 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:51:13,483 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:53:13,484 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:53:13,956 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:53:13,956 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A23%3A13.489545Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:53:13,957 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:53:15,816 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:53:15,817 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:53:15,817 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:53:15,817 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:53:15,817 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:53:15,823 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:53:30,277 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:53:30,285 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:53:30,285 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:53:30,297 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:53:30,301 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:53:30,302 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:55:15,825 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:55:16,222 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:55:16,223 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A25%3A15.826618Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:55:16,223 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:55:16,739 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:55:16,739 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:55:16,739 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:55:16,740 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:55:16,740 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:55:16,745 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:56:44,581 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:56:44,589 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:56:44,590 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:56:44,601 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:56:44,604 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:56:44,604 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:57:16,745 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:57:17,105 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:57:17,105 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A27%3A16.759100Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:57:17,105 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:57:17,846 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:57:17,847 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:57:17,847 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:57:17,847 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:57:17,847 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:57:17,855 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 22:57:19,567 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:57:19,573 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:57:19,574 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:57:19,596 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:57:19,603 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:57:19,604 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:58:11,038 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:58:11,057 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:58:11,057 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:58:11,069 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 22:58:11,072 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 22:58:11,072 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 22:59:17,855 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 22:59:18,345 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 22:59:18,346 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A29%3A17.858776Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 22:59:18,346 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 22:59:19,239 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 22:59:19,240 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 22:59:19,240 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 22:59:19,240 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 22:59:19,240 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 22:59:19,256 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:01:19,285 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:01:19,441 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:01:19,441 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A31%3A19.287411Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:01:19,441 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:01:20,086 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:01:20,087 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:01:20,087 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:01:20,087 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:01:20,087 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:01:20,093 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:03:20,112 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:03:20,460 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:03:20,461 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A33%3A20.115653Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:03:20,461 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:03:21,025 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:03:21,026 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:03:21,026 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:03:21,026 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:03:21,026 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:03:21,033 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:03:22,222 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:03:22,229 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:03:22,229 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:03:22,250 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:03:22,254 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:03:22,254 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:05:21,034 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:05:22,093 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:05:22,093 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A35%3A21.036327Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:05:22,093 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:05:24,572 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:05:24,573 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:05:24,573 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:05:24,573 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:05:24,573 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:05:24,582 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:07:24,583 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:07:24,920 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:07:24,921 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A37%3A24.586668Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:07:24,921 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:07:25,419 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:07:25,420 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:07:25,420 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:07:25,420 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:07:25,420 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:07:25,426 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:09:25,452 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:09:25,805 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:09:25,806 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A39%3A25.456321Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:09:25,807 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:09:26,806 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:09:26,807 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:09:26,807 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:09:26,807 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:09:26,807 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:09:26,812 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:11:26,813 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:11:27,650 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:11:27,650 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A41%3A26.815639Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:11:27,651 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:11:28,560 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:11:28,561 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:11:28,561 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:11:28,561 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:11:28,561 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:11:28,568 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:13:28,568 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:13:28,690 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:13:28,690 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A43%3A28.571408Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:13:28,690 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:13:29,291 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:13:29,292 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:13:29,292 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:13:29,292 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:13:29,292 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:13:29,298 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:15:29,298 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:15:29,797 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:15:29,797 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A45%3A29.301015Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:15:29,798 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:15:30,329 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:15:30,329 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:15:30,329 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:15:30,330 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:15:30,330 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:15:30,335 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:17:30,321 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:17:30,685 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:17:30,686 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A47%3A30.334447Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:17:30,686 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:17:31,122 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:17:31,123 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:17:31,123 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:17:31,123 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:17:31,123 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:17:31,129 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:19:31,131 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:19:31,474 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:19:31,474 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A49%3A31.150503Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:19:31,474 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:19:31,971 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:19:31,974 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:19:31,975 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:19:31,976 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:19:31,981 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:19:32,022 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:21:32,022 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:21:32,369 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:21:32,370 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A51%3A32.026304Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:21:32,370 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:21:32,892 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:21:32,894 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:21:32,894 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:21:32,894 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:21:32,894 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:21:32,902 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:23:32,915 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:23:33,240 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:23:33,240 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A53%3A32.940441Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:23:33,240 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:23:34,381 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:23:34,382 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:23:34,382 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:23:34,382 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:23:34,382 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:23:34,388 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:25:34,389 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:25:34,745 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:25:34,745 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A55%3A34.393679Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:25:34,745 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:25:35,367 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:25:35,367 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:25:35,367 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:25:35,367 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:25:35,367 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:25:35,373 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:27:35,374 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:27:35,779 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:27:35,779 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A57%3A35.378144Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:27:35,779 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:27:36,416 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:27:36,417 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:27:36,417 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:27:36,417 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:27:36,417 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:27:36,423 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:29:36,424 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:29:36,842 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:29:36,843 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T17%3A59%3A36.427183Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:29:36,843 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:29:38,412 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:29:38,412 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:29:38,413 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:29:38,413 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:29:38,413 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:29:38,419 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:31:38,419 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:31:38,780 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:31:38,780 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A01%3A38.428022Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:31:38,781 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:31:39,984 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:31:39,984 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:31:39,984 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:31:39,984 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:31:39,985 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:31:39,993 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:33:39,994 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:33:40,137 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:33:40,137 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A03%3A40.017629Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:33:40,137 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:33:40,853 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:33:40,854 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:33:40,854 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:33:40,854 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:33:40,854 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:33:40,860 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:35:40,860 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:35:40,987 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:35:40,988 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A05%3A40.865670Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:35:40,988 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:35:41,466 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:35:41,467 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:35:41,467 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:35:41,467 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:35:41,467 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:35:41,475 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:37:22,739 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:22,751 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:22,751 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:22,767 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:22,772 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:22,773 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:22,899 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:22,909 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:22,909 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:22,973 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:22,978 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:22,978 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:23,554 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:23,562 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:23,562 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:23,573 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:23,580 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:23,580 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:38,357 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:38,369 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:38,369 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:38,378 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:37:38,383 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:37:38,383 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:37:41,457 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:37:41,887 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:37:41,887 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A07%3A41.459783Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:37:41,887 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:37:42,944 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:37:42,945 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:37:42,946 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:37:42,946 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:37:42,946 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:37:42,953 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:38:17,064 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:38:17,071 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:38:17,072 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:38:17,080 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:38:17,083 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:38:17,083 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:39:42,947 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:39:43,471 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:39:43,472 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A09%3A42.962387Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:39:43,472 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:39:43,939 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:39:43,940 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:39:43,940 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:39:43,940 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:39:43,940 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:39:43,949 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:41:43,949 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:41:44,076 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:41:44,094 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A11%3A43.954535Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:41:44,094 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:41:44,803 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:41:44,804 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:41:44,804 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:41:44,804 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:41:44,804 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:41:44,811 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:43:44,811 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:43:45,151 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:43:45,152 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A13%3A44.815973Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:43:45,152 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:43:45,850 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:43:45,851 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:43:45,851 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:43:45,851 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:43:45,851 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:43:45,857 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:45:44,207 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:45:44,214 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:45:44,214 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:45:44,232 - backend.forecasting - INFO - Training forecasting model...
2025-08-13 23:45:44,237 - backend.forecasting - WARNING - Insufficient historical data for training. Using fallback mode.
2025-08-13 23:45:44,237 - backend.forecasting - INFO - Generating fallback forecast...
2025-08-13 23:45:45,857 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:45:46,170 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:45:46,171 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A15%3A45.859025Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:45:46,171 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:45:46,726 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:45:46,727 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:45:46,727 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:45:46,727 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:45:46,727 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:45:46,735 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:47:46,734 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:47:47,056 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:47:47,057 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A17%3A46.737532Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:47:47,057 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:47:47,634 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:47:47,635 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:47:47,635 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:47:47,635 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:47:47,635 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:47:47,641 - backend.crisis_detection - INFO - Detected 0 new anomalies
2025-08-13 23:49:47,641 - backend.data_ingestion - INFO - Starting data collection from all sources...
2025-08-13 23:49:47,951 - googleapiclient.http - WARNING - Encountered 403 Forbidden with reason "quotaExceeded"
2025-08-13 23:49:47,951 - backend.data_ingestion - ERROR - Error collecting YouTube data: <HttpError 403 when requesting https://youtube.googleapis.com/youtube/v3/search?q=Tesla&part=id%2Csnippet&maxResults=25&order=relevance&publishedAfter=2025-08-06T18%3A19%3A47.643912Z&key=AIzaSyDzCMRB70zDU95rRqZZMuINoIKr5wJdyTE&alt=json returned "The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.". Details: "[{'message': 'The request cannot be completed because you have exceeded your <a href="/youtube/v3/getting-started#quota">quota</a>.', 'domain': 'youtube.quota', 'reason': 'quotaExceeded'}]">
2025-08-13 23:49:47,951 - backend.data_ingestion - ERROR - Error collecting News data: Date input should be in format of either YYYY-MM-DD or YYYY-MM-DDTHH:MM:SS
2025-08-13 23:49:48,504 - backend.data_ingestion - ERROR - Error collecting Reddit data: received 401 HTTP response
2025-08-13 23:49:48,505 - backend.data_ingestion - INFO - Collected 0 items from Reddit
2025-08-13 23:49:48,505 - backend.data_ingestion - INFO - Collected 0 items from YouTube
2025-08-13 23:49:48,505 - backend.data_ingestion - INFO - Collected 0 items from News
2025-08-13 23:49:48,505 - backend.data_ingestion - INFO - Total unique items collected: 0
2025-08-13 23:49:48,511 - backend.crisis_detection - INFO - Detected 0 new anomalies
