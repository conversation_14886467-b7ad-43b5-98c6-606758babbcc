Requirement already satisfied: httpx in /Users/<USER>/.pyenv/versions/3.11.9/lib/python3.11/site-packages (0.13.3)
Collecting httpx
  Using cached httpx-0.28.1-py3-none-any.whl.metadata (7.1 kB)
Requirement already satisfied: anyio in /Users/<USER>/.pyenv/versions/3.11.9/lib/python3.11/site-packages (from httpx) (3.7.1)
Requirement already satisfied: certifi in /Users/<USER>/.pyenv/versions/3.11.9/lib/python3.11/site-packages (from httpx) (2025.6.15)
Collecting httpcore==1.* (from httpx)
  Using cached httpcore-1.0.9-py3-none-any.whl.metadata (21 kB)
Requirement already satisfied: idna in /Users/<USER>/.pyenv/versions/3.11.9/lib/python3.11/site-packages (from httpx) (2.10)
Collecting h11>=0.16 (from httpcore==1.*->httpx)
  Using cached h11-0.16.0-py3-none-any.whl.metadata (8.3 kB)
Requirement already satisfied: sniffio>=1.1 in /Users/<USER>/.pyenv/versions/3.11.9/lib/python3.11/site-packages (from anyio->httpx) (1.3.1)
Using cached httpx-0.28.1-py3-none-any.whl (73 kB)
Using cached httpcore-1.0.9-py3-none-any.whl (78 kB)
Using cached h11-0.16.0-py3-none-any.whl (37 kB)
Installing collected packages: h11, httpcore, httpx
  Attempting uninstall: h11
    Found existing installation: h11 0.9.0
    Uninstalling h11-0.9.0:
      Successfully uninstalled h11-0.9.0
  Attempting uninstall: httpcore
    Found existing installation: httpcore 0.9.1
    Uninstalling httpcore-0.9.1:
      Successfully uninstalled httpcore-0.9.1
  Attempting uninstall: httpx
    Found existing installation: httpx 0.13.3
    Uninstalling httpx-0.13.3:
      Successfully uninstalled httpx-0.13.3

Successfully installed h11-0.16.0 httpcore-1.0.9 httpx-0.28.1
